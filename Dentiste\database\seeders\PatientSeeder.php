<?php

namespace Database\Seeders;

use App\Models\Patient;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PatientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $patients = [
            [
                'first_name' => '<PERSON>',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '+**********',
                'date_of_birth' => '1985-03-15',
                'gender' => 'male',
                'address' => '123 Main Street',
                'city' => 'New York',
                'postal_code' => '10001',
                'emergency_contact_name' => '<PERSON>',
                'emergency_contact_phone' => '+**********',
                'medical_history' => 'No significant medical history',
                'allergies' => 'Penicillin',
                'medications' => 'None',
                'insurance_provider' => 'Blue Cross Blue Shield',
                'insurance_number' => 'BC123456789',
                'status' => 'active',
                'notes' => 'Regular patient, very punctual',
            ],
            [
                'first_name' => 'Sarah',
                'last_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+**********',
                'date_of_birth' => '1990-07-22',
                'gender' => 'female',
                'address' => '456 Oak Avenue',
                'city' => 'Los Angeles',
                'postal_code' => '90210',
                'emergency_contact_name' => 'Mike Johnson',
                'emergency_contact_phone' => '+**********',
                'medical_history' => 'Diabetes Type 2',
                'allergies' => 'None known',
                'medications' => 'Metformin 500mg',
                'insurance_provider' => 'Aetna',
                'insurance_number' => 'AET987654321',
                'status' => 'active',
                'notes' => 'Requires pre-medication for procedures',
            ],
            [
                'first_name' => 'Michael',
                'last_name' => 'Brown',
                'email' => '<EMAIL>',
                'phone' => '+**********',
                'date_of_birth' => '1978-11-08',
                'gender' => 'male',
                'address' => '789 Pine Street',
                'city' => 'Chicago',
                'postal_code' => '60601',
                'emergency_contact_name' => 'Lisa Brown',
                'emergency_contact_phone' => '+**********',
                'medical_history' => 'Hypertension',
                'allergies' => 'Latex',
                'medications' => 'Lisinopril 10mg',
                'insurance_provider' => 'Cigna',
                'insurance_number' => 'CIG456789123',
                'status' => 'active',
                'notes' => 'Anxious patient, prefers morning appointments',
            ],
            [
                'first_name' => 'Emily',
                'last_name' => 'Davis',
                'email' => '<EMAIL>',
                'phone' => '+**********',
                'date_of_birth' => '1995-02-14',
                'gender' => 'female',
                'address' => '321 Elm Street',
                'city' => 'Houston',
                'postal_code' => '77001',
                'emergency_contact_name' => 'Robert Davis',
                'emergency_contact_phone' => '+**********',
                'medical_history' => 'No significant medical history',
                'allergies' => 'Shellfish',
                'medications' => 'Birth control pills',
                'insurance_provider' => 'United Healthcare',
                'insurance_number' => 'UHC789123456',
                'status' => 'active',
                'notes' => 'Student, prefers evening appointments',
            ],
            [
                'first_name' => 'Robert',
                'last_name' => 'Wilson',
                'email' => null,
                'phone' => '+**********',
                'date_of_birth' => '1965-09-30',
                'gender' => 'male',
                'address' => '654 Maple Drive',
                'city' => 'Phoenix',
                'postal_code' => '85001',
                'emergency_contact_name' => 'Mary Wilson',
                'emergency_contact_phone' => '+**********',
                'medical_history' => 'Heart disease, previous MI',
                'allergies' => 'Aspirin',
                'medications' => 'Clopidogrel, Atorvastatin',
                'insurance_provider' => 'Medicare',
                'insurance_number' => 'MED123456789',
                'status' => 'active',
                'notes' => 'Requires antibiotic prophylaxis',
            ],
        ];

        foreach ($patients as $patient) {
            Patient::create($patient);
        }
    }
}
