<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'DentaCare Pro') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700&display=swap" rel="stylesheet" />

        <!-- Icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Custom Styles -->
        <style>
            :root {
                --primary: #667eea;
                --primary-dark: #5a67d8;
                --secondary: #764ba2;
                --accent: #f093fb;
                --success: #48bb78;
                --warning: #ed8936;
                --danger: #f56565;
                --dark: #2d3748;
                --light: #f7fafc;
                --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
                --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
                --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
                --shadow-xl: 0 20px 25px rgba(0,0,0,0.15);
            }

            * {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            body {
                font-family: 'Inter', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                position: relative;
            }

            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
                z-index: -1;
                animation: float 20s ease-in-out infinite;
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); }
                33% { transform: translateY(-20px) rotate(1deg); }
                66% { transform: translateY(10px) rotate(-1deg); }
            }

            /* Glass morphism effect */
            .glass {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }

            .glass-dark {
                background: rgba(0, 0, 0, 0.1);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            /* Modern buttons */
            .btn-primary {
                background: var(--gradient-primary);
                color: white;
                font-weight: 600;
                padding: 12px 24px;
                border-radius: 12px;
                border: none;
                box-shadow: var(--shadow-md);
                position: relative;
                overflow: hidden;
                transform: translateY(0);
            }

            .btn-primary::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            }

            .btn-primary:hover::before {
                left: 100%;
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }

            .btn-secondary {
                background: var(--gradient-secondary);
                color: white;
                font-weight: 600;
                padding: 12px 24px;
                border-radius: 12px;
                border: none;
                box-shadow: var(--shadow-md);
            }

            .btn-secondary:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }

            /* Modern cards */
            .card {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                box-shadow: var(--shadow-md);
                position: relative;
                overflow: hidden;
            }

            .card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 1px;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
            }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: var(--shadow-xl);
            }

            /* Animations */
            .fade-in {
                animation: fadeIn 0.6s ease-out forwards;
                opacity: 0;
            }

            @keyframes fadeIn {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
            }

            .slide-in-left {
                animation: slideInLeft 0.6s ease-out forwards;
                opacity: 0;
            }

            @keyframes slideInLeft {
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
                from {
                    opacity: 0;
                    transform: translateX(-50px);
                }
            }

            .scale-in {
                animation: scaleIn 0.5s ease-out forwards;
                opacity: 0;
                transform: scale(0.9);
            }

            @keyframes scaleIn {
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            /* Stagger animation delays */
            .stagger-1 { animation-delay: 0.1s; }
            .stagger-2 { animation-delay: 0.2s; }
            .stagger-3 { animation-delay: 0.3s; }
            .stagger-4 { animation-delay: 0.4s; }
            .stagger-5 { animation-delay: 0.5s; }
            .stagger-6 { animation-delay: 0.6s; }

            /* Gradient text */
            .gradient-text {
                background: var(--gradient-primary);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            /* Floating elements */
            .float {
                animation: floating 3s ease-in-out infinite;
            }

            @keyframes floating {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-10px); }
            }

            /* Pulse effect */
            .pulse {
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }

            /* Custom scrollbar */
            ::-webkit-scrollbar {
                width: 8px;
            }

            ::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
            }

            ::-webkit-scrollbar-thumb {
                background: var(--gradient-primary);
                border-radius: 10px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: var(--primary-dark);
            }
        </style>
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen">
            @include('layouts.navigation')

            <!-- Page Heading -->
            @isset($header)
                <header class="glass-dark border-b border-white/10">
                    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
                        <div class="fade-in">
                            {{ $header }}
                        </div>
                    </div>
                </header>
            @endisset

            <!-- Page Content -->
            <main class="relative z-10">
                {{ $slot }}
            </main>
        </div>

        <!-- Floating particles -->
        <div class="fixed inset-0 pointer-events-none z-0">
            <div class="absolute top-20 left-10 w-2 h-2 bg-white/20 rounded-full float stagger-1"></div>
            <div class="absolute top-40 right-20 w-3 h-3 bg-white/15 rounded-full float stagger-2"></div>
            <div class="absolute bottom-40 left-20 w-2 h-2 bg-white/25 rounded-full float stagger-3"></div>
            <div class="absolute bottom-20 right-10 w-4 h-4 bg-white/10 rounded-full float stagger-4"></div>
            <div class="absolute top-60 left-1/2 w-2 h-2 bg-white/20 rounded-full float stagger-5"></div>
        </div>

        <!-- Loading overlay -->
        <div id="loading-overlay" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
            <div class="glass rounded-2xl p-8 flex items-center space-x-4">
                <div class="w-8 h-8 border-4 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span class="text-white font-medium text-lg">Loading...</span>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Add stagger animations to elements
                const fadeElements = document.querySelectorAll('.fade-in');
                fadeElements.forEach((el, index) => {
                    el.style.animationDelay = `${index * 0.1}s`;
                });

                const scaleElements = document.querySelectorAll('.scale-in');
                scaleElements.forEach((el, index) => {
                    el.style.animationDelay = `${index * 0.15}s`;
                });

                const slideElements = document.querySelectorAll('.slide-in-left');
                slideElements.forEach((el, index) => {
                    el.style.animationDelay = `${index * 0.1}s`;
                });

                // Add loading animation to forms
                const forms = document.querySelectorAll('form');
                const loadingOverlay = document.getElementById('loading-overlay');

                forms.forEach(form => {
                    form.addEventListener('submit', function() {
                        loadingOverlay.classList.remove('hidden');
                    });
                });

                // Add hover effects to cards
                const cards = document.querySelectorAll('.card');
                cards.forEach(card => {
                    card.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-8px) scale(1.02)';
                    });

                    card.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0) scale(1)';
                    });
                });
            });
        </script>
    </body>
</html>
