<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('staff', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone');
            $table->enum('role', ['dentist', 'receptionist', 'admin', 'hygienist']);
            $table->string('license_number')->nullable();
            $table->text('specializations')->nullable();
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->time('work_start_time')->default('09:00:00');
            $table->time('work_end_time')->default('17:00:00');
            $table->json('work_days')->default('["monday", "tuesday", "wednesday", "thursday", "friday"]');
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->date('hire_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('staff');
    }
};
