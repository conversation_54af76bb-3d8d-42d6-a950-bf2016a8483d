<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Staff extends Model
{
    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'role',
        'license_number',
        'specializations',
        'hourly_rate',
        'work_start_time',
        'work_end_time',
        'work_days',
        'status',
        'hire_date',
    ];

    protected $casts = [
        'work_days' => 'array',
        'hire_date' => 'date',
        'hourly_rate' => 'decimal:2',
    ];

    /**
     * Get the user associated with this staff member.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all appointments for this staff member.
     */
    public function appointments(): Has<PERSON>any
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get all patient treatments performed by this staff member.
     */
    public function patientTreatments(): Has<PERSON><PERSON>
    {
        return $this->hasMany(PatientTreatment::class);
    }

    /**
     * Get the staff member's full name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Scope to get active staff members.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get staff members by role.
     */
    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }
}
