<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Staff;
use Illuminate\Console\Command;

class FixUserStaff extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:user-staff';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix users without staff records';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking users and staff records...');

        // Get all users
        $users = User::all();
        $this->info("Found {$users->count()} users");

        foreach ($users as $user) {
            $this->line("User: {$user->name} ({$user->email})");

            if (!$user->staff) {
                $this->warn("  - No staff record found");

                // Create a staff record for this user
                $staff = Staff::create([
                    'user_id' => $user->id,
                    'first_name' => explode(' ', $user->name)[0] ?? 'Unknown',
                    'last_name' => explode(' ', $user->name)[1] ?? 'User',
                    'email' => $user->email,
                    'phone' => '+**********',
                    'role' => 'admin', // Default to admin for existing users
                    'license_number' => 'TEMP' . $user->id,
                    'specializations' => 'General Administration',
                    'hourly_rate' => 50.00,
                    'work_start_time' => '09:00:00',
                    'work_end_time' => '17:00:00',
                    'work_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                    'status' => 'active',
                    'hire_date' => now(),
                ]);

                $this->info("  - Created staff record with ID: {$staff->id}");
            } else {
                $this->info("  - Staff record exists: {$user->staff->role}");
            }
        }

        $this->info('Done!');
    }
}
