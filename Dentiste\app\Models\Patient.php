<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Patient extends Model
{
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'date_of_birth',
        'gender',
        'address',
        'city',
        'postal_code',
        'emergency_contact_name',
        'emergency_contact_phone',
        'medical_history',
        'allergies',
        'medications',
        'insurance_provider',
        'insurance_number',
        'status',
        'notes',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
    ];

    /**
     * Get the patient's full name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get all appointments for this patient.
     */
    public function appointments(): Has<PERSON>any
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get all treatments for this patient.
     */
    public function patientTreatments(): HasMany
    {
        return $this->hasMany(PatientTreatment::class);
    }

    /**
     * Get all invoices for this patient.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Scope to get active patients.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
