<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Patient;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    public function index(): View
    {
        $user = auth()->user();

        // Get basic statistics
        $stats = [
            'total_patients' => Patient::count(),
            'todays_appointments' => Appointment::today()->count(),
            'pending_invoices' => Invoice::where('status', 'sent')->count(),
            'overdue_invoices' => Invoice::overdue()->count(),
        ];

        // Get today's appointments
        $todaysAppointments = Appointment::with(['patient', 'staff', 'treatment'])
            ->today()
            ->orderBy('appointment_date')
            ->get();

        // Get recent patients
        $recentPatients = Patient::latest()
            ->take(5)
            ->get();

        return view('dashboard', compact('stats', 'todaysAppointments', 'recentPatients'));
    }
}
