<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PatientTreatment extends Model
{
    protected $fillable = [
        'patient_id',
        'treatment_id',
        'staff_id',
        'appointment_id',
        'treatment_date',
        'actual_price',
        'actual_duration_minutes',
        'notes',
        'complications',
        'status',
        'tooth_numbers',
        'prescription',
        'follow_up_date',
    ];

    protected $casts = [
        'treatment_date' => 'date',
        'actual_price' => 'decimal:2',
        'tooth_numbers' => 'array',
        'follow_up_date' => 'date',
    ];

    /**
     * Get the patient for this treatment.
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the treatment for this patient treatment.
     */
    public function treatment(): BelongsTo
    {
        return $this->belongsTo(Treatment::class);
    }

    /**
     * Get the staff member who performed this treatment.
     */
    public function staff(): BelongsTo
    {
        return $this->belongsTo(Staff::class);
    }

    /**
     * Get the appointment associated with this treatment.
     */
    public function appointment(): BelongsTo
    {
        return $this->belongsTo(Appointment::class);
    }

    /**
     * Get the invoice items for this patient treatment.
     */
    public function invoiceItems(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * Scope to get completed treatments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get treatments by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
