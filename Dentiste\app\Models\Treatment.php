<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Treatment extends Model
{
    protected $fillable = [
        'name',
        'description',
        'category',
        'default_price',
        'default_duration_minutes',
        'preparation_instructions',
        'aftercare_instructions',
        'requires_anesthesia',
        'status',
    ];

    protected $casts = [
        'default_price' => 'decimal:2',
        'requires_anesthesia' => 'boolean',
    ];

    /**
     * Get all appointments for this treatment.
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get all patient treatments for this treatment.
     */
    public function patientTreatments(): Has<PERSON>any
    {
        return $this->hasMany(PatientTreatment::class);
    }

    /**
     * Scope to get active treatments.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get treatments by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }
}
