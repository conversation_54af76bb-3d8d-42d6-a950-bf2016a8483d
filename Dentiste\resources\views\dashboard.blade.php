<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div class="fade-in">
                <h1 class="text-4xl font-bold text-white mb-2">
                    Welcome back, {{ Auth::user()->name }}! 👋
                </h1>
                <p class="text-white/80 text-lg">Here's what's happening at your dental practice today</p>
            </div>
            <div class="hidden md:flex items-center space-x-6 fade-in stagger-2">
                <div class="text-right">
                    <div class="text-white/70 text-sm">{{ now()->format('l') }}</div>
                    <div class="text-white text-xl font-bold">{{ now()->format('M d, Y') }}</div>
                </div>
                <div class="w-16 h-16 glass rounded-2xl flex items-center justify-center float">
                    <i class="fas fa-calendar-day text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                <!-- Total Patients Card -->
                <div class="card scale-in stagger-1 group">
                    <div class="p-8">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-gray-500 mb-2">Total Patients</div>
                                <div class="text-4xl font-bold gradient-text mb-2">{{ $totalPatients ?? 0 }}</div>
                                <div class="text-sm text-green-600 flex items-center">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>+12% from last month</span>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-users text-white text-3xl"></i>
                                </div>
                                <div class="absolute -inset-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-3xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-lg"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today's Appointments Card -->
                <div class="card scale-in stagger-2 group">
                    <div class="p-8">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-gray-500 mb-2">Today's Appointments</div>
                                <div class="text-4xl font-bold gradient-text mb-2">{{ $todaysAppointments ?? 0 }}</div>
                                <div class="text-sm text-blue-600 flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    <span>{{ ($todaysAppointments ?? 0) > 0 ? 'Next at 9:00 AM' : 'No appointments' }}</span>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-teal-600 rounded-3xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-calendar-check text-white text-3xl"></i>
                                </div>
                                <div class="absolute -inset-2 bg-gradient-to-r from-green-400 to-teal-500 rounded-3xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-lg"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pending Invoices Card -->
                <div class="card scale-in stagger-3 group">
                    <div class="p-8">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-gray-500 mb-2">Pending Invoices</div>
                                <div class="text-4xl font-bold gradient-text mb-2">{{ $pendingInvoices ?? 0 }}</div>
                                <div class="text-sm text-yellow-600 flex items-center">
                                    <i class="fas fa-hourglass-half mr-1"></i>
                                    <span>Awaiting payment</span>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="w-20 h-20 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-3xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-file-invoice-dollar text-white text-3xl"></i>
                                </div>
                                <div class="absolute -inset-2 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-3xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-lg"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Revenue Card -->
                <div class="card scale-in stagger-4 group">
                    <div class="p-8">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-gray-500 mb-2">Monthly Revenue</div>
                                <div class="text-4xl font-bold gradient-text mb-2">${{ number_format($monthlyRevenue ?? 12450) }}</div>
                                <div class="text-sm text-green-600 flex items-center">
                                    <i class="fas fa-chart-line mr-1"></i>
                                    <span>+8% from last month</span>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="w-20 h-20 bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-dollar-sign text-white text-3xl"></i>
                                </div>
                                <div class="absolute -inset-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-3xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-lg"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
                <a href="{{ route('patients.create') }}" class="btn-primary text-center group scale-in stagger-1 block">
                    <i class="fas fa-user-plus mr-2 group-hover:rotate-12 transition-transform duration-300"></i>
                    Add New Patient
                </a>
                <button class="btn-secondary text-center group scale-in stagger-2 w-full">
                    <i class="fas fa-calendar-plus mr-2 group-hover:bounce transition-transform duration-300"></i>
                    Schedule Appointment
                </button>
                <button class="bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-semibold py-4 px-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group scale-in stagger-3 w-full">
                    <i class="fas fa-file-invoice mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                    Create Invoice
                </button>
                <button class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold py-4 px-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group scale-in stagger-4 w-full">
                    <i class="fas fa-chart-bar mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                    View Reports
                </button>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Today's Appointments -->
                <div class="lg:col-span-2">
                    <div class="card fade-in stagger-5">
                        <div class="p-8">
                            <div class="flex items-center justify-between mb-8">
                                <h3 class="text-2xl font-bold text-gray-900 flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                                        <i class="fas fa-calendar-day text-white"></i>
                                    </div>
                                    Today's Schedule
                                </h3>
                                <span class="glass px-4 py-2 rounded-full text-sm font-medium text-gray-700 border border-gray-200">
                                    {{ ($todaysAppointmentsList ?? collect())->count() }} appointments
                                </span>
                            </div>

                            @if(isset($todaysAppointmentsList) && $todaysAppointmentsList->count() > 0)
                                <div class="space-y-4">
                                    @foreach($todaysAppointmentsList as $index => $appointment)
                                        <div class="appointment-card p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl border border-blue-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 slide-in-left stagger-{{ $index + 1 }}">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                                                        <span class="text-white font-bold text-lg">{{ substr($appointment->patient->first_name ?? 'J', 0, 1) }}{{ substr($appointment->patient->last_name ?? 'D', 0, 1) }}</span>
                                                    </div>
                                                    <div>
                                                        <div class="font-bold text-gray-900 text-lg">
                                                            {{ $appointment->patient->full_name ?? 'John Doe' }}
                                                        </div>
                                                        <div class="text-sm text-gray-600 flex items-center">
                                                            <i class="fas fa-tooth text-blue-500 mr-2"></i>
                                                            {{ $appointment->treatment->name ?? 'General Consultation' }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <div class="text-2xl font-bold text-blue-600">
                                                        {{ $appointment->appointment_date ? $appointment->appointment_date->format('H:i') : '09:00' }}
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        with {{ $appointment->staff->full_name ?? 'Dr. Smith' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <div class="mt-8 text-center">
                                    <button class="text-blue-600 hover:text-blue-800 font-semibold hover:underline transition-all duration-300">
                                        View All Appointments →
                                    </button>
                                </div>
                            @else
                                <div class="text-center py-16">
                                    <div class="w-32 h-32 glass rounded-full flex items-center justify-center mx-auto mb-6">
                                        <i class="fas fa-calendar-times text-gray-400 text-4xl"></i>
                                    </div>
                                    <h4 class="text-2xl font-bold text-gray-900 mb-4">No appointments today</h4>
                                    <p class="text-gray-500 mb-8 text-lg">Looks like you have a free day!</p>
                                    <button class="btn-primary">
                                        <i class="fas fa-calendar-plus mr-2"></i>
                                        Schedule New Appointment
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-8">
                    <!-- Recent Patients -->
                    <div class="card fade-in stagger-6">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-gray-900 flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-friends text-white"></i>
                                    </div>
                                    Recent Patients
                                </h3>
                                <a href="{{ route('patients.index') }}" class="text-blue-600 hover:text-blue-800 text-sm font-semibold hover:underline transition-all duration-300">
                                    View All
                                </a>
                            </div>

                            @if(isset($recentPatients) && $recentPatients->count() > 0)
                                <div class="space-y-4">
                                    @foreach($recentPatients as $index => $patient)
                                        <div class="flex items-center space-x-4 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl hover:shadow-md transition-all duration-300 hover:-translate-y-1 scale-in stagger-{{ $index + 1 }}">
                                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                                                <span class="text-white font-bold">{{ substr($patient->first_name ?? 'J', 0, 1) }}{{ substr($patient->last_name ?? 'D', 0, 1) }}</span>
                                            </div>
                                            <div class="flex-1">
                                                <div class="font-semibold text-gray-900">
                                                    {{ $patient->full_name ?? 'John Doe' }}
                                                </div>
                                                <div class="text-sm text-gray-500 flex items-center">
                                                    <i class="fas fa-phone text-gray-400 mr-1"></i>
                                                    {{ $patient->phone ?? '(*************' }}
                                                </div>
                                            </div>
                                            <div class="text-xs text-gray-400">
                                                {{ $patient->created_at ? $patient->created_at->diffForHumans() : '2 days ago' }}
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <div class="w-16 h-16 glass rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-user-plus text-gray-400 text-2xl"></i>
                                    </div>
                                    <p class="text-gray-500 mb-4">No patients yet</p>
                                    <a href="{{ route('patients.create') }}" class="text-blue-600 hover:text-blue-800 font-semibold text-sm hover:underline transition-all duration-300">
                                        Add your first patient →
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="card fade-in stagger-7">
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                                <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-chart-pie text-white"></i>
                                </div>
                                Quick Stats
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                                    <span class="text-gray-700 font-medium">This Week</span>
                                    <span class="font-bold text-purple-600 text-lg">{{ $weeklyAppointments ?? 24 }} appointments</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-teal-50 rounded-xl">
                                    <span class="text-gray-700 font-medium">Revenue</span>
                                    <span class="font-bold text-green-600 text-lg">${{ number_format($weeklyRevenue ?? 12450) }}</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
                                    <span class="text-gray-700 font-medium">Avg. per day</span>
                                    <span class="font-bold text-blue-600 text-lg">{{ $avgPatientsPerDay ?? '4.8' }} patients</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications -->
                    <div class="card fade-in stagger-8">
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                                <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-bell text-white"></i>
                                </div>
                                Notifications
                            </h3>
                            <div class="space-y-3">
                                <div class="flex items-start space-x-3 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl border-l-4 border-yellow-400">
                                    <i class="fas fa-exclamation-triangle text-yellow-500 mt-1"></i>
                                    <div>
                                        <div class="text-sm font-semibold text-gray-900">Payment Overdue</div>
                                        <div class="text-xs text-gray-600">John Doe - Invoice #1234</div>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border-l-4 border-blue-400">
                                    <i class="fas fa-info-circle text-blue-500 mt-1"></i>
                                    <div>
                                        <div class="text-sm font-semibold text-gray-900">Appointment Reminder</div>
                                        <div class="text-xs text-gray-600">Sarah Johnson - Tomorrow 2:00 PM</div>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3 p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-xl border-l-4 border-green-400">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <div>
                                        <div class="text-sm font-semibold text-gray-900">Treatment Completed</div>
                                        <div class="text-xs text-gray-600">Mike Brown - Root Canal</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
