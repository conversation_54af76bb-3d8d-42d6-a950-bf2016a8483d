<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('login');
});

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified', 'role:admin,dentist,receptionist,hygienist'])
    ->name('dashboard');

// Protected routes that require authentication and staff role
Route::middleware(['auth', 'verified', 'role:admin,dentist,receptionist,hygienist'])->group(function () {
    // Patient management routes
    Route::resource('patients', PatientController::class);

    // Debug route to test patient creation
    Route::get('/test-patients', function () {
        return view('patients.create');
    })->name('test.patients');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
