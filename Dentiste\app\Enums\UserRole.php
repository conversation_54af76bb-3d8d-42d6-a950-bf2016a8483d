<?php

namespace App\Enums;

enum UserRole: string
{
    case ADMIN = 'admin';
    case DENTIST = 'dentist';
    case RECEPTIONIST = 'receptionist';
    case HYGIENIST = 'hygienist';

    public function label(): string
    {
        return match($this) {
            self::ADMIN => 'Administrator',
            self::DENTIST => 'Dentist',
            self::RECEPTIONIST => 'Receptionist',
            self::HYGIENIST => 'Hygienist',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
