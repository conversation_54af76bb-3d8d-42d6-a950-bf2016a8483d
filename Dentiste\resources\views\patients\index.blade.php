<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div class="fade-in">
                <h1 class="text-4xl font-bold text-white mb-2 flex items-center">
                    <div class="w-12 h-12 glass rounded-2xl flex items-center justify-center mr-4">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                    Patient Management
                </h1>
                <p class="text-white/80 text-lg">Manage your dental practice patients with style</p>
            </div>
            <div class="flex items-center space-x-4 fade-in stagger-2">
                <div class="hidden sm:flex items-center space-x-2 text-white/80 glass px-4 py-2 rounded-xl">
                    <i class="fas fa-users"></i>
                    <span class="font-medium">{{ $patients->total() }} total patients</span>
                </div>
                <a href="{{ route('patients.create') }}" class="btn-primary group">
                    <i class="fas fa-user-plus mr-2 group-hover:rotate-12 transition-transform duration-300"></i>
                    Add New Patient
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="glass border border-green-400/30 text-white px-6 py-4 rounded-2xl mb-8 flex items-center fade-in">
                    <i class="fas fa-check-circle text-green-400 mr-3 text-xl"></i>
                    <span class="font-medium text-lg">{{ session('success') }}</span>
                </div>
            @endif

            <!-- Search and Filter Form -->
            <div class="card mb-8 fade-in">
                <div class="p-8">
                    <form method="GET" action="{{ route('patients.index') }}" class="space-y-6 sm:space-y-0 sm:flex sm:items-end sm:space-x-6">
                        <div class="flex-1">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">Search Patients</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400 text-lg"></i>
                                </div>
                                <input type="text" name="search" value="{{ request('search') }}"
                                       placeholder="Search by name, email, or phone..."
                                       class="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 text-lg">
                            </div>
                        </div>
                        <div class="sm:w-48">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">Status</label>
                            <select name="status" class="w-full py-4 px-4 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 text-lg">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        <div class="flex space-x-4">
                            <button type="submit" class="btn-primary group">
                                <i class="fas fa-search mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                                Search
                            </button>
                            @if(request()->hasAny(['search', 'status']))
                                <a href="{{ route('patients.index') }}" class="px-6 py-4 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold rounded-2xl transition-all duration-300 hover:-translate-y-1 flex items-center">
                                    <i class="fas fa-times mr-2"></i>
                                    Clear
                                </a>
                            @endif
                        </div>
                    </form>
                </div>
            </div>

            <!-- Patients Grid -->
            @if($patients->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                    @foreach($patients as $index => $patient)
                        <div class="card scale-in stagger-{{ ($index % 6) + 1 }} group">
                            <div class="p-8">
                                <!-- Patient Header -->
                                <div class="flex items-center space-x-4 mb-6">
                                    <div class="relative">
                                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                                            <span class="text-white text-2xl font-bold">{{ substr($patient->first_name, 0, 1) }}{{ substr($patient->last_name, 0, 1) }}</span>
                                        </div>
                                        <div class="absolute -inset-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-3xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-lg"></div>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="text-xl font-bold text-gray-900 mb-1">{{ $patient->full_name }}</h3>
                                        <div class="flex items-center space-x-3">
                                            <span class="px-3 py-1 text-xs font-semibold rounded-full
                                                {{ $patient->status === 'active' ? 'bg-gradient-to-r from-green-400 to-green-600 text-white' : 'bg-gradient-to-r from-red-400 to-red-600 text-white' }}">
                                                {{ ucfirst($patient->status) }}
                                            </span>
                                            <span class="text-sm text-gray-500 font-medium">{{ $patient->date_of_birth->age }} years old</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Patient Info -->
                                <div class="space-y-4 mb-8">
                                    <div class="flex items-center text-gray-600">
                                        <div class="w-8 h-8 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-envelope text-blue-600"></i>
                                        </div>
                                        <span class="font-medium">{{ $patient->email ?: 'No email provided' }}</span>
                                    </div>
                                    <div class="flex items-center text-gray-600">
                                        <div class="w-8 h-8 bg-gradient-to-br from-green-100 to-teal-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-phone text-green-600"></i>
                                        </div>
                                        <span class="font-medium">{{ $patient->phone }}</span>
                                    </div>
                                    <div class="flex items-center text-gray-600">
                                        <div class="w-8 h-8 bg-gradient-to-br from-yellow-100 to-orange-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-map-marker-alt text-yellow-600"></i>
                                        </div>
                                        <span class="font-medium">{{ $patient->city }}</span>
                                    </div>
                                    @if($patient->insurance_provider)
                                        <div class="flex items-center text-gray-600">
                                            <div class="w-8 h-8 bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-shield-alt text-purple-600"></i>
                                            </div>
                                            <span class="font-medium">{{ $patient->insurance_provider }}</span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex items-center justify-between pt-6 border-t border-gray-100">
                                    <div class="flex space-x-2">
                                        <a href="{{ route('patients.show', $patient) }}"
                                           class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-semibold rounded-xl transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl">
                                            <i class="fas fa-eye mr-1"></i>
                                            View
                                        </a>
                                        <a href="{{ route('patients.edit', $patient) }}"
                                           class="px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white text-sm font-semibold rounded-xl transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl">
                                            <i class="fas fa-edit mr-1"></i>
                                            Edit
                                        </a>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="px-4 py-2 bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600 text-white text-sm font-semibold rounded-xl transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl">
                                            <i class="fas fa-calendar-plus mr-1"></i>
                                            Book
                                        </button>
                                        <form action="{{ route('patients.destroy', $patient) }}" method="POST" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                    class="px-4 py-2 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white text-sm font-semibold rounded-xl transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl"
                                                    onclick="return confirm('Are you sure you want to delete this patient?')">
                                                <i class="fas fa-trash mr-1"></i>
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="flex justify-center fade-in">
                    <div class="glass rounded-2xl p-4 border border-white/20">
                        {{ $patients->appends(request()->query())->links() }}
                    </div>
                </div>
            @else
                <div class="card text-center py-20 fade-in">
                    <div class="w-40 h-40 glass rounded-full flex items-center justify-center mx-auto mb-8">
                        <i class="fas fa-user-plus text-gray-400 text-6xl"></i>
                    </div>
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">No patients found</h3>
                    <p class="text-gray-500 mb-8 text-lg">Get started by adding your first patient to the system</p>
                    <a href="{{ route('patients.create') }}" class="btn-primary text-xl px-8 py-4">
                        <i class="fas fa-user-plus mr-3"></i>
                        Add First Patient
                    </a>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
