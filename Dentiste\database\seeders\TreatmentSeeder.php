<?php

namespace Database\Seeders;

use App\Models\Treatment;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TreatmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $treatments = [
            [
                'name' => 'Dental Cleaning',
                'description' => 'Regular dental cleaning and examination',
                'category' => 'Preventive',
                'default_price' => 80.00,
                'default_duration_minutes' => 45,
                'preparation_instructions' => 'Brush teeth before appointment',
                'aftercare_instructions' => 'Avoid eating for 30 minutes',
                'requires_anesthesia' => false,
            ],
            [
                'name' => 'Tooth Filling',
                'description' => 'Composite or amalgam tooth filling',
                'category' => 'Restorative',
                'default_price' => 150.00,
                'default_duration_minutes' => 60,
                'preparation_instructions' => 'No special preparation needed',
                'aftercare_instructions' => 'Avoid hard foods for 24 hours',
                'requires_anesthesia' => true,
            ],
            [
                'name' => 'Root Canal',
                'description' => 'Root canal treatment',
                'category' => 'Endodontic',
                'default_price' => 800.00,
                'default_duration_minutes' => 90,
                'preparation_instructions' => 'Take prescribed antibiotics if given',
                'aftercare_instructions' => 'Take pain medication as prescribed, avoid chewing on treated side',
                'requires_anesthesia' => true,
            ],
            [
                'name' => 'Tooth Extraction',
                'description' => 'Simple tooth extraction',
                'category' => 'Oral Surgery',
                'default_price' => 200.00,
                'default_duration_minutes' => 30,
                'preparation_instructions' => 'Eat light meal before appointment',
                'aftercare_instructions' => 'Apply ice pack, avoid smoking, soft foods only',
                'requires_anesthesia' => true,
            ],
            [
                'name' => 'Dental Crown',
                'description' => 'Porcelain or metal crown placement',
                'category' => 'Restorative',
                'default_price' => 1200.00,
                'default_duration_minutes' => 120,
                'preparation_instructions' => 'No special preparation needed',
                'aftercare_instructions' => 'Avoid sticky foods, maintain good oral hygiene',
                'requires_anesthesia' => true,
            ],
            [
                'name' => 'Teeth Whitening',
                'description' => 'Professional teeth whitening treatment',
                'category' => 'Cosmetic',
                'default_price' => 300.00,
                'default_duration_minutes' => 60,
                'preparation_instructions' => 'Avoid staining foods 24 hours before',
                'aftercare_instructions' => 'Avoid staining foods and drinks for 48 hours',
                'requires_anesthesia' => false,
            ],
        ];

        foreach ($treatments as $treatment) {
            Treatment::create($treatment);
        }
    }
}
