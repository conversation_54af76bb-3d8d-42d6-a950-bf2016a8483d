<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Staff;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user (skip if exists)
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Dr. Admin',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        // Create staff record for admin (skip if exists)
        Staff::firstOrCreate(
            ['user_id' => $adminUser->id],
            [
                'first_name' => 'Dr.',
                'last_name' => 'Admin',
                'email' => '<EMAIL>',
                'phone' => '+**********',
                'role' => 'admin',
                'license_number' => 'ADM001',
                'specializations' => 'General Dentistry, Administration',
                'hourly_rate' => 100.00,
                'work_start_time' => '08:00:00',
                'work_end_time' => '18:00:00',
                'work_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                'status' => 'active',
                'hire_date' => now()->subYears(2),
            ]
        );

        // Create a dentist user
        $dentistUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Dr. Smith',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        // Create staff record for dentist
        Staff::firstOrCreate(
            ['user_id' => $dentistUser->id],
            [
                'first_name' => 'Dr. John',
                'last_name' => 'Smith',
                'email' => '<EMAIL>',
                'phone' => '+**********',
                'role' => 'dentist',
                'license_number' => 'DEN001',
                'specializations' => 'General Dentistry, Oral Surgery',
                'hourly_rate' => 80.00,
                'work_start_time' => '09:00:00',
                'work_end_time' => '17:00:00',
                'work_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                'status' => 'active',
                'hire_date' => now()->subYear(),
            ]
        );

        // Create a receptionist user
        $receptionistUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Jane Doe',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        // Create staff record for receptionist
        Staff::firstOrCreate(
            ['user_id' => $receptionistUser->id],
            [
                'first_name' => 'Jane',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '+**********',
                'role' => 'receptionist',
                'license_number' => null,
                'specializations' => null,
                'hourly_rate' => 25.00,
                'work_start_time' => '08:00:00',
                'work_end_time' => '17:00:00',
                'work_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                'status' => 'active',
                'hire_date' => now()->subMonths(6),
            ]
        );
    }
}
