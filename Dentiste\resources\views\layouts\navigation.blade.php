<nav x-data="{ open: false }" class="glass sticky top-0 z-50 border-b border-white/10">
    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-20">
            <div class="flex items-center">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('dashboard') }}" class="flex items-center space-x-3 group">
                        <div class="relative">
                            <div class="w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/20 group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-tooth text-white text-xl group-hover:rotate-12 transition-transform duration-300"></i>
                            </div>
                            <div class="absolute -inset-1 bg-gradient-to-r from-pink-300 to-blue-300 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur"></div>
                        </div>
                        <div class="hidden sm:block">
                            <h1 class="text-2xl font-bold text-white group-hover:text-pink-200 transition-colors duration-300">
                                DentaCare Pro
                            </h1>
                            <p class="text-sm text-white/70 -mt-1">Practice Management</p>
                        </div>
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden space-x-2 sm:ms-10 sm:flex">
                    <a href="{{ route('dashboard') }}"
                       class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                        <i class="fas fa-chart-line mr-2"></i>
                        Dashboard
                    </a>
                    <a href="{{ route('patients.index') }}"
                       class="nav-link {{ request()->routeIs('patients.*') ? 'active' : '' }}">
                        <i class="fas fa-users mr-2"></i>
                        Patients
                    </a>
                    <a href="#" class="nav-link">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Appointments
                    </a>
                    <a href="#" class="nav-link">
                        <i class="fas fa-file-invoice-dollar mr-2"></i>
                        Billing
                    </a>
                </div>
            </div>

            <!-- Right Side -->
            <div class="hidden sm:flex sm:items-center sm:space-x-4">
                <!-- Notifications -->
                <button class="relative p-3 text-white/80 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-300 group">
                    <i class="fas fa-bell text-lg group-hover:animate-pulse"></i>
                    <span class="absolute -top-1 -right-1 bg-gradient-to-r from-pink-500 to-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center pulse">3</span>
                </button>

                <!-- User Dropdown -->
                <div class="relative" x-data="{ open: false }" @click.outside="open = false">
                    <button @click="open = !open" class="flex items-center space-x-3 px-4 py-2 glass-dark hover:bg-white/10 rounded-2xl transition-all duration-300 group border border-white/20">
                        <div class="w-10 h-10 bg-gradient-to-br from-pink-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <span class="text-white text-sm font-bold">{{ substr(Auth::user()->name, 0, 1) }}</span>
                        </div>
                        <div class="text-left hidden md:block">
                            <div class="text-sm font-semibold text-white">{{ Auth::user()->name }}</div>
                            <div class="text-xs text-white/70">{{ Auth::user()->staff->role ?? 'Staff' }}</div>
                        </div>
                        <i class="fas fa-chevron-down text-white/60 text-xs transition-transform duration-300" :class="{ 'rotate-180': open }"></i>
                    </button>

                    <div x-show="open"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute right-0 mt-2 w-64 glass-dark rounded-2xl border border-white/20 overflow-hidden shadow-xl z-50"
                         style="display: none;">

                        <div class="px-6 py-4 border-b border-white/10">
                            <div class="text-sm font-medium text-white">{{ Auth::user()->name }}</div>
                            <div class="text-sm text-white/70">{{ Auth::user()->email }}</div>
                        </div>

                        <div class="py-2">
                            <a href="{{ route('profile.edit') }}" class="flex items-center space-x-3 px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200">
                                <i class="fas fa-user-cog text-white/60"></i>
                                <span>Profile Settings</span>
                            </a>

                            <a href="#" class="flex items-center space-x-3 px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200">
                                <i class="fas fa-cog text-white/60"></i>
                                <span>Preferences</span>
                            </a>

                            <div class="border-t border-white/10 my-2"></div>

                            <!-- Authentication -->
                            <form method="POST" action="{{ route('logout') }}" class="w-full">
                                @csrf
                                <button type="submit" class="w-full flex items-center space-x-3 px-6 py-3 text-red-300 hover:text-red-200 hover:bg-red-500/10 transition-all duration-200 text-left">
                                    <i class="fas fa-sign-out-alt text-red-400"></i>
                                    <span>Log Out</span>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hamburger -->
            <div class="-me-2 flex items-center sm:hidden">
                <button @click="open = ! open" class="inline-flex items-center justify-center p-3 rounded-xl text-white/80 hover:text-white hover:bg-white/10 focus:outline-none transition-all duration-300">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Responsive Navigation Menu -->
    <div :class="{'block': open, 'hidden': ! open}" class="hidden sm:hidden glass-dark border-t border-white/10">
        <div class="pt-4 pb-3 space-y-2 px-4">
            <a href="{{ route('dashboard') }}" class="mobile-nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                <i class="fas fa-chart-line mr-3"></i>
                Dashboard
            </a>
            <a href="{{ route('patients.index') }}" class="mobile-nav-link {{ request()->routeIs('patients.*') ? 'active' : '' }}">
                <i class="fas fa-users mr-3"></i>
                Patients
            </a>
            <a href="#" class="mobile-nav-link">
                <i class="fas fa-calendar-alt mr-3"></i>
                Appointments
            </a>
            <a href="#" class="mobile-nav-link">
                <i class="fas fa-file-invoice-dollar mr-3"></i>
                Billing
            </a>
        </div>

        <!-- Responsive Settings Options -->
        <div class="pt-4 pb-4 border-t border-white/10">
            <div class="px-4 mb-4">
                <div class="font-medium text-base text-white">{{ Auth::user()->name }}</div>
                <div class="font-medium text-sm text-white/70">{{ Auth::user()->email }}</div>
            </div>

            <div class="space-y-2 px-4">
                <a href="{{ route('profile.edit') }}" class="mobile-nav-link">
                    <i class="fas fa-user-cog mr-3"></i>
                    Profile
                </a>

                <!-- Authentication -->
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="mobile-nav-link w-full text-left text-red-300 hover:text-red-200 hover:bg-red-500/10">
                        <i class="fas fa-sign-out-alt mr-3"></i>
                        Log Out
                    </button>
                </form>
            </div>
        </div>
    </div>
</nav>

<style>
    .nav-link {
        @apply px-4 py-3 text-sm font-medium text-white/80 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-300 flex items-center relative;
    }

    .nav-link.active {
        @apply text-white bg-white/20 shadow-lg;
    }

    .nav-link.active::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 2px;
        background: linear-gradient(90deg, #f093fb, #f5576c);
        border-radius: 1px;
    }

    .nav-link:hover {
        transform: translateY(-1px);
    }

    .mobile-nav-link {
        @apply block px-4 py-3 text-base font-medium text-white/80 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-300 flex items-center;
    }

    .mobile-nav-link.active {
        @apply text-white bg-white/20;
    }
</style>
