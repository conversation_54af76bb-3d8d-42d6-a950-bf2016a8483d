<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $patient->full_name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('patients.edit', $patient) }}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                    Edit Patient
                </a>
                <a href="{{ route('patients.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Patients
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Patient Information -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Full Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->full_name }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Date of Birth</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->date_of_birth->format('M d, Y') }} ({{ $patient->date_of_birth->age }} years old)</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Gender</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ ucfirst($patient->gender) }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Status</label>
                                    <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {{ $patient->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ ucfirst($patient->status) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Phone</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->phone }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Email</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->email ?: 'Not provided' }}</p>
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-500">Address</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->address }}</p>
                                    <p class="text-sm text-gray-900">{{ $patient->city }}, {{ $patient->postal_code }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Emergency Contact -->
                    @if($patient->emergency_contact_name || $patient->emergency_contact_phone)
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->emergency_contact_name ?: 'Not provided' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Phone</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->emergency_contact_phone ?: 'Not provided' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Medical Information -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Medical Information</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Medical History</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->medical_history ?: 'No medical history recorded' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Allergies</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->allergies ?: 'No known allergies' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Current Medications</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->medications ?: 'No current medications' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Insurance Information -->
                    @if($patient->insurance_provider || $patient->insurance_number)
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Insurance Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Provider</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->insurance_provider ?: 'Not provided' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Policy Number</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $patient->insurance_number ?: 'Not provided' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Notes -->
                    @if($patient->notes)
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Notes</h3>
                            <p class="text-sm text-gray-900">{{ $patient->notes }}</p>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Quick Actions -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-2">
                                <button class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Schedule Appointment
                                </button>
                                <button class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Add Treatment
                                </button>
                                <button class="w-full bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                                    Create Invoice
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Appointments -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Appointments</h3>
                            @if($patient->appointments->count() > 0)
                                <div class="space-y-3">
                                    @foreach($patient->appointments->take(5) as $appointment)
                                        <div class="border-l-4 border-blue-400 pl-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $appointment->appointment_date->format('M d, Y H:i') }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ $appointment->treatment->name ?? 'General Consultation' }}
                                            </div>
                                            <div class="text-xs text-gray-400">
                                                with {{ $appointment->staff->full_name }}
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-sm text-gray-500">No appointments yet.</p>
                            @endif
                        </div>
                    </div>

                    <!-- Treatment History -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Treatment History</h3>
                            @if($patient->patientTreatments->count() > 0)
                                <div class="space-y-3">
                                    @foreach($patient->patientTreatments->take(5) as $treatment)
                                        <div class="border-l-4 border-green-400 pl-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $treatment->treatment->name }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ $treatment->treatment_date->format('M d, Y') }}
                                            </div>
                                            <div class="text-xs text-gray-400">
                                                ${{ number_format($treatment->actual_price, 2) }}
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-sm text-gray-500">No treatments yet.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
